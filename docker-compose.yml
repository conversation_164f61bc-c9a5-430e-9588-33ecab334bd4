services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      FLASK_ENV: production
      SECRET_KEY: defaultsecretkey123
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: habit_tracker
      DB_USER: admin
      DB_PASSWORD: adminpassword
      PYTHONPATH: /app
      PYTHONUNBUFFERED: 1
    ports:
      - "5000:5000"
    networks:
      - habit-tracker-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:15-alpine
    container_name: habit-tracker-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: habit_tracker
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: adminpassword
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - habit-tracker-network
    healthcheck:
      test:
        ["CMD-SHELL", "pg_isready -U habitadmin -d habit_tracker"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  pgadmin:
    image: dpage/pgadmin4
    container_name: habit-tracker-pgadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: password
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - habit-tracker-network

networks:
  habit-tracker-network:
    driver: bridge
    name: habit-tracker-network

volumes:
  postgres_data:
    name: habit-tracker-postgres-data
    driver: local
