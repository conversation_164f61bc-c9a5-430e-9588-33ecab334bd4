# Example Environment Configuration for Habit Tracker
# Copy this file to .env and update the values

# Container Configuration (for production deployment)
CONTAINER_IMAGE=ghcr.io/your-username/the-habit-tracker:latest

# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=habit_tracker
DB_USER=admin
DB_PASSWORD=your-db-password

# PgAdmin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=your-pgadmin-password
