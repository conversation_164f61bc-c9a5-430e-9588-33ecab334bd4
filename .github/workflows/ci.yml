name: Docker CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

permissions:
  contents: read
  packages: write

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    uses: ./.github/workflows/test.yml

  build:
    needs: test
    uses: ./.github/workflows/build.yml

  deploy:
    needs: [build]
    uses: ./.github/workflows/deploy.yml
    with:
      image: ${{ needs.build.outputs.image }}
    secrets: inherit
  