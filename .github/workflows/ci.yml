name: Docker CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    uses: ./.github/workflows/test.yml

  build:
    needs: test
    uses: ./.github/workflows/build.yml
  
  terraform-apply:
    needs: docker-build
    uses: ./.github/workflows/terraform-apply.yml
    secrets: inherit

  deploy:
    needs: terraform-apply
    uses: ./.github/workflows/deploy.yml
    secrets: inherit
  