name: Build image

on:
  workflow_call:
    outputs:
      image:
        description: "Built Docker image tag"
        value: ${{ jobs.docker-build.outputs.image }}

env:
  REGISTRY: ghcr.io

jobs:
  docker-build:
    runs-on: ubuntu-latest
    outputs:
      image: ${{ steps.image.outputs.image }}
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Generate lowercase image name
        id: image-name
        run: |
          IMAGE_NAME=$(echo "${{ github.repository }}" | tr '[:upper:]' '[:lower:]')
          echo "image-name=$IMAGE_NAME" >> $GITHUB_OUTPUT
          echo "full-image=${{ env.REGISTRY }}/$IMAGE_NAME" >> $GITHUB_OUTPUT

      - name: Log in to Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ steps.image-name.outputs.full-image }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          target: production
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Output image
        id: image
        run: echo "image=${{ steps.image-name.outputs.full-image }}:sha-${{ github.sha }}" >> $GITHUB_OUTPUT