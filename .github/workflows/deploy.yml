name: Deploy to VM

on:
  workflow_call:
    inputs:
      image:
        required: true
        type: string
    secrets:
      DEPLOY_VM_IP:
        required: true
      DEPLOY_SSH_KEY:
        required: true
      DB_USER:
        required: true
      DB_PASSWORD:
        required: true
      SECRET_KEY:
        required: true
      FLASK_ENV:
        required: true
      PGADMIN_EMAIL:
        required: true
      PGADMIN_PASSWORD:
        required: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    environment: production
    env:
      VM_IP: ${{ secrets.DEPLOY_VM_IP }}
      SSH_PRIVATE_KEY: ${{ secrets.DEPLOY_SSH_KEY }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # Azure Login removed - not needed for deployment via SSH

      - name: Setup SSH key
        run: |
          echo "🔍 VM IP: $VM_IP"
          echo "🔍 SSH key length: $(echo "$SSH_PRIVATE_KEY" | wc -c) characters"

          if [ -z "$VM_IP" ]; then
            echo "❌ VM IP is empty. Check DEPLOY_VM_IP secret."
            exit 1
          fi

          if [ -z "$SSH_PRIVATE_KEY" ]; then
            echo "❌ SSH private key is empty. Check DEPLOY_SSH_KEY secret."
            exit 1
          fi

          echo "✅ VM IP: $VM_IP"
          echo "✅ SSH key length: $(echo "$SSH_PRIVATE_KEY" | wc -c) characters"

          # Setup SSH directory and key
          mkdir -p ~/.ssh

          # Write SSH key with proper formatting and newlines
          printf "%s\n" "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa

          # Ensure proper permissions
          chmod 600 ~/.ssh/id_rsa
          chmod 700 ~/.ssh

          # Verify key format
          echo "🔍 Verifying SSH key format..."
          if ssh-keygen -l -f ~/.ssh/id_rsa >/dev/null 2>&1; then
            echo "✅ SSH key format is valid"
          else
            echo "❌ SSH key format is invalid"
            echo "🔍 Key starts with: $(head -1 ~/.ssh/id_rsa)"
            echo "🔍 Key ends with: $(tail -1 ~/.ssh/id_rsa)"
            echo "🔍 Attempting to fix key format..."

            # Try to fix common formatting issues
            sed -i 's/\\n/\n/g' ~/.ssh/id_rsa

            if ssh-keygen -l -f ~/.ssh/id_rsa >/dev/null 2>&1; then
              echo "✅ SSH key format fixed"
            else
              echo "❌ Could not fix SSH key format"
              exit 1
            fi
          fi

          # Create SSH config for better connection handling
          cat > ~/.ssh/config << EOF
          Host vm
            HostName $VM_IP
            User azureuser
            IdentityFile ~/.ssh/id_rsa
            StrictHostKeyChecking no
            UserKnownHostsFile /dev/null
            ServerAliveInterval 60
            ServerAliveCountMax 3
            ConnectTimeout 30
          EOF
          chmod 600 ~/.ssh/config

          echo "✅ VM is ready (verified by terraform-apply stage)"

      - name: Create deployment environment file
        run: |
          echo "🔍 Creating deployment environment file..."

          cat > .env.prod << EOF
          # Container Configuration
          CONTAINER_IMAGE=${{ inputs.image }}

          # Flask Configuration
          FLASK_ENV=${{ secrets.FLASK_ENV }}
          SECRET_KEY=${{ secrets.SECRET_KEY }}

          # Database Configuration
          DB_USER=${{ secrets.DB_USER }}
          DB_PASSWORD=${{ secrets.DB_PASSWORD }}

          # PgAdmin Configuration
          PGADMIN_EMAIL=${{ secrets.PGADMIN_EMAIL }}
          PGADMIN_PASSWORD=${{ secrets.PGADMIN_PASSWORD }}
          EOF

          echo "✅ Environment file created"

      - name: Copy deployment files to VM
        run: |
          echo "📁 Copying deployment files to VM..."
          scp .env.prod vm:/tmp/
          scp docker-compose.yml vm:/tmp/
          echo "✅ Files copied successfully"

      - name: Deploy application with Docker Compose
        run: |
          echo "🚀 Starting deployment on VM..."
          ssh vm << 'EOF'
            # Check cloud-init status
            echo "🔍 Checking cloud-init status..."
            sudo cloud-init status --wait || echo "⚠️ Cloud-init may still be running"

            # Show cloud-init logs if there were issues
            if ! sudo cloud-init status | grep -q "done"; then
              echo "🔍 Cloud-init logs (last 20 lines):"
              sudo tail -20 /var/log/cloud-init-output.log || echo "No cloud-init output log found"
            fi

            # Check if Docker is installed, install if missing
            if ! command -v docker >/dev/null 2>&1; then
              echo "🐳 Docker not found, installing..."
              curl -fsSL https://get.docker.com -o get-docker.sh
              sudo sh get-docker.sh
              sudo systemctl start docker
              sudo systemctl enable docker
              sudo usermod -aG docker azureuser
              echo "✅ Docker installed"
            else
              echo "✅ Docker is already installed"
            fi

            # Ensure Docker is running
            sudo systemctl start docker

            # Setup deployment directory
            sudo mkdir -p /opt/habit-tracker
            sudo cp /tmp/.env.prod /opt/habit-tracker/
            sudo cp /tmp/docker-compose.yml /opt/habit-tracker/
            cd /opt/habit-tracker

            # Login to GitHub Container Registry
            echo "${{ secrets.GITHUB_TOKEN }}" | sudo docker login ghcr.io -u ${{ github.actor }} --password-stdin

            # Stop existing services
            sudo docker compose down || true

            # Pull latest images
            sudo docker compose pull

            # Start services
            sudo docker compose up -d

            # Wait for services to be healthy
            echo "⏳ Waiting for services to be healthy..."
            for i in {1..30}; do
              if sudo docker compose ps | grep -q "healthy"; then
                echo "✅ Services are healthy"
                break
              else
                echo "⏳ Waiting for services... (attempt $i/30)"
                sleep 10
              fi
            done

            # Test application
            sleep 10
            curl -f http://localhost:5000/ || exit 1
            echo "✅ Application deployed successfully with Docker Compose!"
          EOF

      - name: Cleanup
        if: always()
        run: |
          rm -f ~/.ssh/id_rsa
          rm -f .env.prod
