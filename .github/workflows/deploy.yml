name: Deploy to VM

on:
  workflow_call:

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "~1.0"
          terraform_wrapper: false

      - name: Download Terraform State
        uses: actions/download-artifact@v4
        with:
          name: terraform-state
          path: terraform/

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Setup SSH key
        run: |
          VM_IP="${{ needs.terraform-apply.outputs.vm_ip }}"
          SSH_KEY="${{ needs.terraform-apply.outputs.ssh_private_key }}"

          if [ -z "$VM_IP" ]; then
            echo "❌ VM IP is empty. Terraform apply may have failed."
            exit 1
          fi

          if [ -z "$SSH_KEY" ]; then
            echo "❌ SSH private key is empty. Terraform apply may have failed."
            exit 1
          fi

          echo "✅ VM IP: $VM_IP"
          echo "✅ SSH key length: $(echo "$SSH_KEY" | wc -c) characters"

          # Setup SSH directory and key
          mkdir -p ~/.ssh
          echo "$SSH_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          chmod 700 ~/.ssh

          # Create SSH config for better connection handling
          cat > ~/.ssh/config << EOF
          Host vm
            HostName $VM_IP
            User azureuser
            IdentityFile ~/.ssh/id_rsa
            StrictHostKeyChecking no
            UserKnownHostsFile /dev/null
            ServerAliveInterval 60
            ServerAliveCountMax 3
            ConnectTimeout 30
          EOF
          chmod 600 ~/.ssh/config

          # Wait for VM SSH service to be ready
          echo "⏳ Waiting for VM SSH service to be ready..."
          for i in {1..20}; do
            if timeout 10 ssh -o ConnectTimeout=10 -o BatchMode=yes vm "echo 'SSH connection successful'" 2>/dev/null; then
              echo "✅ SSH connection successful on attempt $i"
              break
            else
              echo "⏳ SSH not ready yet, retrying in 15 seconds... (attempt $i/20)"
              if [ $i -eq 20 ]; then
                echo "❌ SSH connection failed after 20 attempts"
                echo "🔍 Debugging information:"
                echo "VM IP: $VM_IP"
                echo "Testing basic connectivity..."
                timeout 5 nc -zv "$VM_IP" 22 || echo "Port 22 not reachable"
                exit 1
              fi
              sleep 15
            fi
          done

          # Wait for VM initialization to complete
          echo "⏳ Waiting for VM initialization to complete..."
          for i in {1..15}; do
            if ssh vm "test -f /tmp/vm-ready" 2>/dev/null; then
              echo "✅ VM initialization completed"
              ssh vm "cat /tmp/vm-ready"
              break
            else
              echo "⏳ VM still initializing... (attempt $i/15)"
              if [ $i -eq 15 ]; then
                echo "⚠️ VM initialization check timed out, but proceeding..."
                break
              fi
              sleep 20
            fi
          done

      - name: Create deployment environment file
        run: |
          echo "🔍 Creating deployment environment file..."

          # Generate a secure database password
          DB_PASSWORD=$(openssl rand -base64 32)

          cat > .env.prod << EOF
          # Container Configuration
          CONTAINER_IMAGE=ghcr.io/${{ github.repository }}:${{ github.sha }}

          # Flask Configuration
          FLASK_ENV=production
          SECRET_KEY=${{ secrets.TF_VAR_FLASK_SECRET_KEY }}

          # Database Configuration (Docker PostgreSQL)
          DB_USER=habitadmin
          DB_PASSWORD=$DB_PASSWORD

          # Optional: PgAdmin Configuration
          PGADMIN_EMAIL=admin@${{ github.repository_owner }}.com
          PGADMIN_PASSWORD=$(openssl rand -base64 16)
          EOF

          echo "✅ Environment file created with secure passwords"

      - name: Copy deployment files to VM
        run: |
          echo "📁 Copying deployment files to VM..."
          scp .env.prod vm:/tmp/
          scp docker-compose.yml vm:/tmp/
          scp docker-compose.prod.yml vm:/tmp/
          scp scripts/init-db.sql vm:/tmp/
          echo "✅ Files copied successfully"

      - name: Deploy application with Docker Compose
        run: |
          echo "🚀 Starting deployment on VM..."
          ssh vm << 'EOF'
            # Setup deployment directory
            sudo mkdir -p /opt/habit-tracker
            sudo cp /tmp/.env.prod /opt/habit-tracker/
            sudo cp /tmp/docker-compose.yml /opt/habit-tracker/
            sudo cp /tmp/docker-compose.prod.yml /opt/habit-tracker/
            sudo mkdir -p /opt/habit-tracker/scripts
            sudo cp /tmp/init-db.sql /opt/habit-tracker/scripts/
            cd /opt/habit-tracker

            # Login to GitHub Container Registry
            echo "${{ secrets.GITHUB_TOKEN }}" | sudo docker login ghcr.io -u ${{ github.actor }} --password-stdin

            # Stop existing services
            sudo docker compose -f docker-compose.yml -f docker-compose.prod.yml down || true

            # Pull latest images
            sudo docker compose -f docker-compose.yml -f docker-compose.prod.yml pull

            # Start services with production overrides
            sudo docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d

            # Wait for services to be healthy
            echo "⏳ Waiting for services to be healthy..."
            for i in {1..30}; do
              if sudo docker compose -f docker-compose.yml -f docker-compose.prod.yml ps | grep -q "healthy"; then
                echo "✅ Services are healthy"
                break
              else
                echo "⏳ Waiting for services... (attempt $i/30)"
                sleep 10
              fi
            done

            # Test application
            sleep 10
            curl -f http://localhost:5000/ || exit 1
            echo "✅ Application deployed successfully with Docker Compose!"
          EOF

      - name: Setup SSL Certificate (if domain configured)
        run: |
          if [ -z "${{ secrets.TF_VAR_DOMAIN_NAME }}" ]; then
            echo "TF_VAR_DOMAIN_NAME secret is not set. Skipping SSL setup."
            exit 0
          fi
          echo "🔒 Setting up SSL certificate..."
          ssh vm << 'EOF'
            sudo sed -i "s/server_name _;/server_name ${{ secrets.TF_VAR_DOMAIN_NAME }};/g" /etc/nginx/sites-available/habit-tracker
            sudo nginx -t
            sudo systemctl reload nginx
            sudo certbot --nginx -d ${{ secrets.TF_VAR_DOMAIN_NAME }} --email ${{ secrets.TF_VAR_SSL_EMAIL }} --agree-tos --non-interactive
            echo "SSL certificate configured successfully!"
          EOF

      - name: Verify deployment
        run: |
          VM_IP=${{ needs.terraform-apply.outputs.vm_ip }}
          echo "Testing HTTP redirect..."
          curl -I http://$VM_IP | grep -q "301\|302" || echo "Warning: HTTP redirect not working"
          echo "Testing application health..."
          curl -f http://$VM_IP/ || curl -f https://$VM_IP/ || {
            echo "Application health check failed"
            exit 1
          }
          echo "Deployment verification completed successfully!"
          echo "Application is available at: http://$VM_IP"
          if [ -n "${{ secrets.TF_VAR_DOMAIN_NAME }}" ]; then
            echo "Domain URL: https://${{ secrets.TF_VAR_DOMAIN_NAME }}"
          fi

      - name: Cleanup
        if: always()
        run: |
          rm -f ~/.ssh/id_rsa
          rm -f .env.prod
