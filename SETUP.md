# Habit Tracker Setup Guide

## Prerequisites

- Azure CLI installed and authenticated
- Terraform CLI installed
- GitHub CLI installed and authenticated
- Docker and Docker Compose installed (for local development)

## 1. Setup Azure Backend for Terraform

Before running Terraform, you need to set up the remote state backend:

```bash
cd terraform
./setup-backend.sh
```

This script will:
- Create a resource group for Terraform state
- Create a storage account with a unique name
- Create a blob container for state files
- Display the storage account name to update in `main.tf`

After running the script, update the `storage_account_name` in `terraform/main.tf` with the actual storage account name provided by the script.

## 2. Configure GitHub Secrets

The following secrets have been created automatically:
- `DB_USER`: Database username
- `DB_PASSWORD`: Secure database password
- `SECRET_KEY`: Flask secret key
- `FLASK_ENV`: Flask environment (production)
- `PGADMIN_EMAIL`: PgAdmin login email
- `PGADMIN_PASSWORD`: PgAdmin password

Additional secrets you need to create manually:
- `AZURE_CREDENTIALS`: Azure service principal credentials for GitHub Actions
- `AZURE_VM_HOST`: VM IP address (set after Terraform apply)
- `AZURE_VM_USER`: VM username (usually 'azureuser')
- `AZURE_VM_SSH_KEY`: Private SSH key for VM access

## 3. Local Development

1. Copy environment template:
   ```bash
   cp .env.example .env
   ```

2. Update `.env` with your local database settings

3. Start the application:
   ```bash
   docker compose up -d
   ```

## 4. Production Deployment

1. Push code to main branch
2. GitHub Actions will automatically:
   - Build Docker image
   - Apply Terraform configuration
   - Deploy to Azure VM

## Infrastructure Details

### VM Configuration
- **OS**: Ubuntu 22.04 LTS
- **Services**: Docker, Nginx, UFW Firewall
- **Ports**: 22 (SSH), 80 (HTTP), 443 (HTTPS)

### Nginx Configuration
- **Main App**: `http://vm-ip/` → `localhost:5000`
- **PgAdmin**: `http://vm-ip/db` → `localhost:8080`

### Security
- UFW firewall enabled
- SSH key-based authentication
- Secure database passwords
- HTTPS ready (SSL certificate setup available)

## Troubleshooting

### Terraform State Issues
If you encounter state duplication issues:
1. Ensure the backend is properly configured
2. Run `terraform init -reconfigure` to reinitialize

### VM Access Issues
1. Check security group rules allow SSH (port 22)
2. Verify SSH key is correctly configured
3. Check VM is running and accessible

### Application Issues
1. Check Docker container logs: `docker compose logs`
2. Verify environment variables are set correctly
3. Ensure database is accessible and initialized
