# =============================================================================
# Production Override for Docker Compose
# =============================================================================

version: "3.8"

services:
  # Production PostgreSQL configuration
  postgres:
    environment:
      # Use stronger settings for production
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=en_US.UTF-8 --lc-ctype=en_US.UTF-8 --auth-host=md5"
    volumes:
      # Add backup directory
      - postgres_backups:/var/lib/postgresql/backups
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=100
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB

  # Production app configuration
  app:
    environment:
      # Production-specific settings
      FLASK_ENV: production
      FLASK_DEBUG: 0
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Remove PgAdmin in production
  pgadmin:
    profiles:
      - never

# Additional production volumes
volumes:
  postgres_backups:
    name: habit-tracker-postgres-backups
    driver: local
