{% extends "base.html" %} {% block title %}Habit Tracker - Home{% endblock %} {%
block content %}
<div class="row mb-4">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center">
      <h1><i class="fas fa-chart-line me-2"></i>Your Habits</h1>
      <a href="{{ url_for('main.new_habit') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>Add New Habit
      </a>
    </div>
  </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
  <div class="col-md-4">
    <div class="card bg-primary text-white">
      <div class="card-body text-center">
        <h5 class="card-title"><i class="fas fa-list me-2"></i>Total Habits</h5>
        <h2 class="mb-0">{{ total_habits }}</h2>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card bg-success text-white">
      <div class="card-body text-center">
        <h5 class="card-title">
          <i class="fas fa-check me-2"></i>Completed Today
        </h5>
        <h2 class="mb-0">{{ completed_today }}</h2>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card bg-info text-white">
      <div class="card-body text-center">
        <h5 class="card-title">
          <i class="fas fa-calendar me-2"></i>Today's Progress
        </h5>
        <h2 class="mb-0">
          {% if total_habits > 0 %} {{ "%.0f"|format((completed_today /
          total_habits) * 100) }}% {% else %} 0% {% endif %}
        </h2>
      </div>
    </div>
  </div>
</div>

{% if habits %}
<div class="row">
  {% for habit in habits %}
  <div class="col-md-6 col-lg-4 mb-4">
    <div class="card habit-card h-100 shadow-sm">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-start mb-3">
          <h5 class="card-title mb-0">{{ habit.name }}</h5>
          <span class="badge bg-secondary">{{ habit.frequency.title() }}</span>
        </div>

        <div class="row text-center mb-3">
          <div class="col-4">
            <div class="progress-circle bg-primary">
              {{ habit.get_current_streak() }}
            </div>
            <small class="text-muted d-block mt-1">Current</small>
          </div>
          <div class="col-4">
            <div class="progress-circle bg-success">
              {{ habit.get_longest_streak() }}
            </div>
            <small class="text-muted d-block mt-1">Longest</small>
          </div>
          <div class="col-4">
            <div class="progress-circle bg-info">
              {{ habit.get_completion_percentage() }}%
            </div>
            <small class="text-muted d-block mt-1">Success</small>
          </div>
        </div>

        <div class="d-flex justify-content-between align-items-center">
          <small class="text-muted">
            Started: {{ habit.start_date.strftime('%b %d, %Y') }}
          </small>
          <div>
            {% if habit.is_completed_today() %}
            <span class="badge bg-success">
              <i class="fas fa-check me-1"></i>Done Today
            </span>
            {% else %}
            <form
              method="POST"
              action="{{ url_for('main.complete_habit', habit_id=habit.id) }}"
              class="d-inline"
            >
              <input
                type="hidden"
                name="csrf_token"
                value="{{ csrf_token() }}"
              />
              <button type="submit" class="btn btn-sm btn-outline-success">
                <i class="fas fa-check me-1"></i>Mark Done
              </button>
            </form>
            {% endif %}
          </div>
        </div>
      </div>
      <div class="card-footer bg-transparent">
        <div class="d-flex justify-content-between">
          <a
            href="{{ url_for('main.habit_detail', habit_id=habit.id) }}"
            class="btn btn-sm btn-outline-primary"
          >
            <i class="fas fa-eye me-1"></i>View Details
          </a>
          <div>
            <a
              href="{{ url_for('main.edit_habit', habit_id=habit.id) }}"
              class="btn btn-sm btn-outline-secondary"
            >
              <i class="fas fa-edit"></i>
            </a>
            <button
              type="button"
              class="btn btn-sm btn-outline-danger"
              data-habit-id="{{ habit.id }}"
              data-habit-name="{{ habit.name }}"
              onclick="confirmDelete(this.dataset.habitId, this.dataset.habitName)"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  {% endfor %}
</div>
{% else %}
<div class="text-center py-5">
  <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
  <h3 class="text-muted">No habits yet!</h3>
  <p class="text-muted">
    Start building good habits by creating your first one.
  </p>
  <a href="{{ url_for('main.new_habit') }}" class="btn btn-primary btn-lg">
    <i class="fas fa-plus me-2"></i>Create Your First Habit
  </a>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Delete Habit</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete "<span id="habitName"></span>"?</p>
        <p class="text-danger"><small>This action cannot be undone.</small></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <form id="deleteForm" method="POST" style="display: inline">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
          <button type="submit" class="btn btn-danger">Delete</button>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block scripts %}
<script>
  function confirmDelete(habitId, habitName) {
    document.getElementById('habitName').textContent = habitName;
    document.getElementById('deleteForm').action = `/habit/${habitId}/delete`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
  }
</script>
{% endblock %}
