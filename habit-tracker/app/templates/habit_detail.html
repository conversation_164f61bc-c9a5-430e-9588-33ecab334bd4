{% extends "base.html" %} {% block title %}Habit Details - {{ habit.name }}{%
endblock %} {% block content %}
<div class="row justify-content-center">
  <div class="col-md-8 col-lg-7">
    <div class="card shadow-sm mb-4">
      <div class="card-body">
        <h2 class="mb-2">{{ habit.name }}</h2>
        <span class="badge bg-secondary mb-2"
          >{{ habit.frequency.title() }}</span
        >
        <p class="mb-1">
          <strong>Started:</strong> {{ habit.start_date.strftime('%b %d, %Y') }}
        </p>
        <div class="row text-center mb-3">
          <div class="col-4">
            <div class="progress-circle bg-primary">
              {{ habit.get_current_streak() }}
            </div>
            <small class="text-muted d-block mt-1">Current Streak</small>
          </div>
          <div class="col-4">
            <div class="progress-circle bg-success">
              {{ habit.get_longest_streak() }}
            </div>
            <small class="text-muted d-block mt-1">Longest Streak</small>
          </div>
          <div class="col-4">
            <div class="progress-circle bg-info">
              {{ habit.get_completion_percentage() }}%
            </div>
            <small class="text-muted d-block mt-1">Completion %</small>
          </div>
        </div>
        <form
          method="POST"
          action="{{ url_for('main.complete_habit', habit_id=habit.id) }}"
        >
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
          {% if habit.is_completed_today() %}
          <button class="btn btn-success mb-2" disabled>
            <i class="fas fa-check me-1"></i>Completed Today
          </button>
          {% else %}
          <button class="btn btn-outline-success mb-2">
            <i class="fas fa-check me-1"></i>Mark as Completed Today
          </button>
          {% endif %}
        </form>
        <div class="d-flex justify-content-between mt-3">
          <a
            href="{{ url_for('main.edit_habit', habit_id=habit.id) }}"
            class="btn btn-outline-secondary"
          >
            <i class="fas fa-edit"></i> Edit
          </a>
          <form
            method="POST"
            action="{{ url_for('main.delete_habit', habit_id=habit.id) }}"
            onsubmit="return confirm('Delete this habit?');"
          >
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
            <button type="submit" class="btn btn-outline-danger">
              <i class="fas fa-trash"></i> Delete
            </button>
          </form>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="card-body">
        <h5>Completion Dates</h5>
        {% if completion_dates %}
        <div>
          {% for d in completion_dates %}
          <span class="completion-date">
            {{ d.strftime('%b %d, %Y') }}
            <form
              method="POST"
              action="{{ url_for('main.uncomplete_habit', habit_id=habit.id, date_str=d.strftime('%Y-%m-%d')) }}"
              style="display: inline"
            >
              <input
                type="hidden"
                name="csrf_token"
                value="{{ csrf_token() }}"
              />
              <button
                type="submit"
                class="btn btn-link btn-sm text-danger p-0 ms-1"
                title="Remove completion"
              >
                <i class="fas fa-times"></i>
              </button>
            </form>
          </span>
          {% endfor %}
        </div>
        {% else %}
        <p class="text-muted">No completions yet.</p>
        {% endif %}
      </div>
    </div>
  </div>
</div>
{% endblock %}
