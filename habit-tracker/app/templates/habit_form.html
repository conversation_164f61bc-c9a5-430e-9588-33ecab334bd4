{% extends "base.html" %} {% block title %}{{ title or 'Habit Form' }}{%
endblock %} {% block content %}
<div class="row justify-content-center">
  <div class="col-md-8 col-lg-6">
    <div class="card shadow-sm">
      <div class="card-body">
        <h2 class="mb-4">{{ title or 'Habit Form' }}</h2>
        <form method="POST">
          {{ form.hidden_tag() }}
          <div class="mb-3">
            {{ form.name.label(class="form-label") }} {{
            form.name(class="form-control", placeholder="Enter habit name") }}
            {% for error in form.name.errors %}
            <div class="text-danger small">{{ error }}</div>
            {% endfor %}
          </div>
          <div class="mb-3">
            {{ form.frequency.label(class="form-label") }} {{
            form.frequency(class="form-select") }} {% for error in
            form.frequency.errors %}
            <div class="text-danger small">{{ error }}</div>
            {% endfor %}
          </div>
          <div class="mb-3">
            {{ form.start_date.label(class="form-label") }} {{
            form.start_date(class="form-control", type="date") }} {% for error
            in form.start_date.errors %}
            <div class="text-danger small">{{ error }}</div>
            {% endfor %}
          </div>
          <div class="d-flex justify-content-between">
            <a href="{{ url_for('main.index') }}" class="btn btn-secondary"
              >Cancel</a
            >
            {{ form.submit(class="btn btn-primary") }}
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}
