# Deployment Setup Guide

This guide will help you set up the complete CI/CD pipeline for the Habit Tracker application.

## Prerequisites

- Azure CLI installed and authenticated (`az login`)
- GitHub CLI installed and authenticated (`gh auth login`)
- Terraform CLI installed
- Docker and Docker Compose (for local development)

## Step 1: Azure Service Principal Setup

Create Azure Service Principal for GitHub Actions authentication:

```bash
./setup-azure-auth.sh
```

This script will:
- Create a Service Principal with Contributor role
- Set up all required GitHub secrets:
  - `ARM_CLIENT_ID`
  - `ARM_CLIENT_SECRET` 
  - `ARM_SUBSCRIPTION_ID`
  - `ARM_TENANT_ID`
  - `AZURE_CREDENTIALS`

## Step 2: Terraform Backend Setup

Set up Azure Storage for Terraform remote state:

```bash
cd terraform
./setup-backend.sh
```

This script will:
- Create a resource group for Terraform state
- Create a storage account with unique name
- Create a blob container
- Automatically update `main.tf` with the storage account name

## Step 3: Verify Configuration

Check that all secrets are properly set:

```bash
gh secret list
```

You should see these secrets:
- `ARM_CLIENT_ID`
- `ARM_CLIENT_SECRET`
- `ARM_SUBSCRIPTION_ID` 
- `ARM_TENANT_ID`
- `AZURE_CREDENTIALS`
- `DB_USER`
- `DB_PASSWORD`
- `SECRET_KEY`
- `FLASK_ENV`
- `PGADMIN_EMAIL`
- `PGADMIN_PASSWORD`

## Step 4: Deploy Infrastructure

Push your code to the main branch to trigger the CI/CD pipeline:

```bash
git add .
git commit -m "Setup CI/CD pipeline"
git push origin main
```

The pipeline will:
1. **Test** - Run unit tests and linting
2. **Build** - Build and push Docker image to GitHub Container Registry
3. **Terraform Apply** - Provision Azure infrastructure
4. **Deploy** - Deploy application to Azure VM

## Step 5: Access Your Application

After successful deployment:

1. **Main Application**: `http://<vm-ip>/`
2. **Database Admin**: `http://<vm-ip>/db`

The VM IP will be displayed in the GitHub Actions logs.

## Local Development

For local development:

1. Copy environment template:
   ```bash
   cp .env.example .env
   ```

2. Update `.env` with your local settings

3. Start the application:
   ```bash
   docker compose up -d
   ```

## Infrastructure Details

### Azure Resources Created
- Resource Group
- Virtual Network with subnet
- Network Security Group (SSH, HTTP, HTTPS)
- Public IP address
- Virtual Machine (Ubuntu 22.04)
- SSH key pair

### VM Configuration
- **OS**: Ubuntu 22.04 LTS
- **Size**: Standard_B2s (configurable)
- **Services**: Docker, Nginx, UFW Firewall
- **Ports**: 22 (SSH), 80 (HTTP), 443 (HTTPS)

### Nginx Reverse Proxy
- **Main App**: `http://vm-ip/` → `localhost:5000`
- **PgAdmin**: `http://vm-ip/db` → `localhost:8080`

## Troubleshooting

### Authentication Issues
- Ensure Azure CLI is logged in: `az login`
- Verify Service Principal has correct permissions
- Check GitHub secrets are properly set

### Terraform Issues
- Ensure backend is properly configured
- Check Azure credentials are valid
- Verify subscription has sufficient permissions

### Deployment Issues
- Check GitHub Actions logs for detailed error messages
- Verify VM is accessible via SSH
- Check Docker containers are running on VM

## Security Notes

- SSH access uses key-based authentication only
- UFW firewall is enabled with minimal required ports
- Database passwords are randomly generated
- All secrets are stored securely in GitHub

## Next Steps

After successful deployment:
1. Configure domain name (optional)
2. Set up SSL certificate with Let's Encrypt
3. Configure monitoring and logging
4. Set up backup procedures
