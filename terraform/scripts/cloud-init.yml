package_update: true
package_upgrade: true

packages:
  - nginx
  - ufw
  - curl
  - wget
  - git
  - unzip

users:
  - name: appuser
    groups: docker
    shell: /bin/bash
    sudo: ["ALL=(ALL) NOPASSWD:ALL"]

runcmd:
  - apt-get update
  - apt-get upgrade -y

  # Install Docker
  - curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
  - echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
  - apt-get update
  - apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
  - systemctl start docker
  - systemctl enable docker
  - usermod -aG docker ${admin_username}
  - usermod -aG docker appuser

  # Configure Nginx
  - systemctl enable nginx
  - systemctl start nginx

  # Create Nginx configuration for habit tracker
  - |
    cat > /etc/nginx/sites-available/habit-tracker << 'EOF'
    server {
        listen 80;
        server_name _;

        # Main application
        location / {
            proxy_pass http://localhost:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # PgAdmin interface
        location /db {
            proxy_pass http://localhost:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
    }
    EOF

  # Enable the site
  - ln -sf /etc/nginx/sites-available/habit-tracker /etc/nginx/sites-enabled/
  - rm -f /etc/nginx/sites-enabled/default
  - nginx -t
  - systemctl reload nginx

  # Configure UFW firewall
  - ufw allow 22/tcp
  - ufw allow 80/tcp
  - ufw allow 443/tcp
  - ufw --force enable

  # Mark VM as ready
  - echo "VM initialization completed at $(date)" > /tmp/vm-ready
