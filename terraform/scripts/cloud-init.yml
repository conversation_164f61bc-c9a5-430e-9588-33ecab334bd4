#cloud-config

package_update: true
package_upgrade: true

packages:
  - nginx
  - ufw
  - curl
  - wget
  - git
  - unzip

users:
  - name: appuser
    groups: docker
    shell: /bin/bash
    sudo: ["ALL=(ALL) NOPASSWD:ALL"]

runcmd:
  # Docker installation (secure + modern way)
  - curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
  - echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" > /etc/apt/sources.list.d/docker.list
  - apt-get update
  - apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
  - systemctl enable docker
  - systemctl start docker
  - usermod -aG docker ${admin_username}
  - usermod -aG docker appuser

  # Configure Nginx
  - systemctl enable nginx
  - systemctl start nginx
  - |
    cat > /etc/nginx/sites-available/habit-tracker << 'EOF'
    server {
        listen 80;
        server_name _;

        location / {
            proxy_pass http://localhost:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /db {
            proxy_pass http://localhost:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    EOF
  - ln -sf /etc/nginx/sites-available/habit-tracker /etc/nginx/sites-enabled/
  - rm -f /etc/nginx/sites-enabled/default
  - nginx -t && systemctl reload nginx

  # UFW firewall rules
  - ufw allow OpenSSH
  - ufw allow 'Nginx Full'
  - ufw --force enable

  # Mark success
  - echo "VM initialization completed at $(date)" > /tmp/vm-ready
