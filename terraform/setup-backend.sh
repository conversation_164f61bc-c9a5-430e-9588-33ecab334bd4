#!/bin/bash

# Setup Azure Backend for Terraform Remote State
# Run this script once before using Terraform

set -e

echo "🚀 Setting up Azure backend for Terraform remote state..."

# Variables
RESOURCE_GROUP_NAME="terraform-state-rg"
STORAGE_ACCOUNT_NAME="tfstate$(openssl rand -hex 4)"
CONTAINER_NAME="tfstate"
LOCATION="East US"

# Check if Azure CLI is logged in
if ! az account show >/dev/null 2>&1; then
    echo "❌ Please login to Azure CLI first: az login"
    exit 1
fi

echo "✅ Azure CLI is authenticated"

# Create resource group
echo "📁 Creating resource group: $RESOURCE_GROUP_NAME"
az group create \
    --name "$RESOURCE_GROUP_NAME" \
    --location "$LOCATION" \
    --tags "Purpose=TerraformState" "Project=habit-tracker"

# Create storage account
echo "💾 Creating storage account: $STORAGE_ACCOUNT_NAME"
az storage account create \
    --resource-group "$RESOURCE_GROUP_NAME" \
    --name "$STORAGE_ACCOUNT_NAME" \
    --sku "Standard_LRS" \
    --encryption-services blob \
    --https-only true \
    --min-tls-version "TLS1_2" \
    --allow-blob-public-access false

# Create blob container
echo "📦 Creating blob container: $CONTAINER_NAME"
az storage container create \
    --name "$CONTAINER_NAME" \
    --account-name "$STORAGE_ACCOUNT_NAME" \
    --auth-mode login

echo "✅ Azure backend setup completed!"
echo ""
echo "📋 Backend Configuration:"
echo "  Resource Group: $RESOURCE_GROUP_NAME"
echo "  Storage Account: $STORAGE_ACCOUNT_NAME"
echo "  Container: $CONTAINER_NAME"
echo ""
echo "🔧 Update your terraform backend configuration with:"
echo "  storage_account_name = \"$STORAGE_ACCOUNT_NAME\""
echo ""
echo "💡 Save this storage account name for your CI/CD pipeline!"

# Update the main.tf file automatically
echo "🔄 Updating terraform/main.tf with the new storage account name..."
sed -i "s/storage_account_name = \"tfstatehabittracker\"/storage_account_name = \"$STORAGE_ACCOUNT_NAME\"/" main.tf

echo "✅ terraform/main.tf updated successfully!"
echo ""
echo "🎉 Backend setup completed! You can now run 'terraform init' to initialize the backend."
